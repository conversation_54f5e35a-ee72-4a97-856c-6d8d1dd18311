/* Hero Section Styles */
.hero-section {
  padding: 20px 0 0 0;
  position: relative;
  z-index: 1;
}

.hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.hero-content {
  display: grid;
  grid-template-columns: 9fr 4fr;
  gap: 20px;
  align-items: stretch;
}

/* Slider Column (3/4) */
.hero-slider-column {
  position: relative;
}

.hero-slider {
  background: rgba(var(--secondary-color), 0.3);
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  height: 400px;
  border: 1px solid rgba(var(--primary-color), 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.slider-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.slider-track {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: grab;
  will-change: transform;
}

.slider-track.dragging {
  transition: none;
  cursor: grabbing;
}

.slide {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
  cursor: pointer;
  position: relative;
}

.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 20px;
}

/* Mobile optimization for hero slider images */
@media (max-width: 768px) {
  .slide-image {
    object-position: left center;
  }
}

/* Slider Dots */
.slider-dots {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: row;
  gap: 12px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.slider-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.slider-dot.active {
  background: rgb(var(--primary-color));
  border-color: rgb(var(--primary-color));
  transform: scale(1.3);
  box-shadow:
    0 0 16px rgba(var(--primary-color), 0.6),
    0 2px 8px rgba(0, 0, 0, 0.4);
}

.slider-dot:hover {
  background: rgba(var(--primary-color), 0.8);
  border-color: rgba(var(--primary-color), 0.9);
  transform: scale(1.1);
  box-shadow:
    0 0 12px rgba(var(--primary-color), 0.4),
    0 2px 8px rgba(0, 0, 0, 0.3);
}

/* CTA Column (1/4) */
.hero-cta-column {
  position: relative;
  background: linear-gradient(145deg, #1a1d29 0%, #252a3a 100%);
  border-radius: 20px;
  padding: 0;
  border: 1px solid rgba(255, 193, 7, 0.2);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 8px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  overflow: hidden;
}

/* User Profile Header */
.user-profile-header {
  background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
  padding: 20px;
  border-radius: 20px 20px 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0;
}

.user-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #ff9800 0%, #ffc107 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 800;
  color: #1a1d29;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.user-info h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 700;
  color: #1a1d29;
  line-height: 1.2;
}

.user-info p {
  margin: 4px 0 0 0;
  font-size: 0.85rem;
  color: rgba(26, 29, 41, 0.7);
  font-weight: 500;
}



/* Balance Card */
.balance-card {
  background: linear-gradient(135deg, #2a2f42 0%, #1f2332 100%);
  margin: 0 20px;
  padding: 20px;
  border-radius: 16px;
  border: 1px solid rgba(255, 193, 7, 0.15);
  position: relative;
  margin-bottom: 20px;
}

.balance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.balance-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.balance-status {
  background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
  color: #1a1d29;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.balance-amount {
  font-size: 2.5rem;
  font-weight: 800;
  color: #ffc107;
  margin: 0;
  line-height: 1;
  text-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.balance-details {
  margin-top: 8px;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

/* Main CTA Button */
.main-cta-button {
  background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
  border: none;
  border-radius: 16px;
  padding: 18px;
  margin: 0 20px 20px 20px;
  font-size: 1.1rem;
  font-weight: 700;
  color: #1a1d29;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow:
    0 8px 24px rgba(255, 193, 7, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.main-cta-button:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 32px rgba(255, 193, 7, 0.4),
    0 6px 16px rgba(0, 0, 0, 0.25);
  background: linear-gradient(135deg, #ff9800 0%, #ffc107 100%);
}

/* Bonus Card */
.bonus-card {
  background: linear-gradient(135deg, #2a2f42 0%, #1f2332 100%);
  margin: 0 20px 20px 20px;
  padding: 20px;
  border-radius: 16px;
  border: 1px solid rgba(255, 193, 7, 0.15);
  display: flex;
  align-items: center;
  gap: 16px;
}

.bonus-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: #1a1d29;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.bonus-text {
  flex: 1;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
  font-weight: 600;
  line-height: 1.3;
}

/* Secondary CTA Button */
.secondary-cta-button {
  background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
  border: none;
  border-radius: 12px;
  padding: 12px 20px;
  font-size: 0.9rem;
  font-weight: 700;
  color: #1a1d29;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.secondary-cta-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(255, 193, 7, 0.4);
}

/* Card Layout Responsive Design */
@media (max-width: 1200px) {
  .user-profile-header {
    padding: 18px;
  }

  .balance-card, .bonus-card {
    margin: 0 18px 18px 18px;
    padding: 18px;
  }

  .main-cta-button {
    margin: 0 18px 18px 18px;
    padding: 16px;
    font-size: 1.05rem;
  }

  .user-avatar {
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }

  .user-info h3 {
    font-size: 1.2rem;
  }

  .balance-amount {
    font-size: 2.2rem;
  }
}

/* Loading State */
.hero-slider.loading {
  background: rgba(var(--secondary-color), 0.2);
}

.hero-slider.loading::after {
  content: 'Slaytlar yükleniyor...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  z-index: 5;
}

/* Error State */
.hero-slider.error::after {
  content: 'Slaytlar yüklenemedi';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 100, 100, 0.8);
  font-size: 1.1rem;
  z-index: 5;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .hero-container {
    padding: 0 20px;
  }

  .hero-content {
    gap: 24px;
  }

  .hero-slider {
    height: 350px;
  }

  .hero-cta-column {
    padding: 28px 20px;
    border-radius: 20px;
  }

  .hero-title {
    font-size: 2.1rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-cta-button {
    padding: 16px 32px;
    font-size: 1.1rem;
  }

  .slider-dots {
    bottom: 16px;
    padding: 6px 14px;
  }
}

@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 2fr 1fr;
    gap: 20px;
  }

  .hero-slider {
    height: 320px;
  }

  .hero-cta-column {
    border-radius: 18px;
  }

  .user-profile-header {
    padding: 16px;
  }

  .balance-card, .bonus-card {
    margin: 0 16px 16px 16px;
    padding: 16px;
  }

  .main-cta-button {
    margin: 0 16px 16px 16px;
    padding: 15px;
    font-size: 1rem;
  }

  .user-avatar {
    width: 42px;
    height: 42px;
    font-size: 1.2rem;
  }

  .user-info h3 {
    font-size: 1.1rem;
  }

  .user-info p {
    font-size: 0.8rem;
  }

  .balance-amount {
    font-size: 2rem;
  }

  .bonus-icon {
    width: 36px;
    height: 36px;
    font-size: 1.1rem;
  }

  .slider-dots {
    bottom: 14px;
    gap: 10px;
    padding: 6px 12px;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 15px 0;
  }

  .hero-container {
    padding: 0 16px;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .hero-slider {
    height: 280px;
  }

  .hero-cta-column {
    border-radius: 20px;
  }

  .user-profile-header {
    padding: 16px;
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .balance-card, .bonus-card {
    margin: 0 16px 16px 16px;
    padding: 16px;
  }

  .main-cta-button {
    margin: 0 16px 16px 16px;
    padding: 16px;
    font-size: 1rem;
  }

  .user-avatar {
    width: 50px;
    height: 50px;
    font-size: 1.4rem;
  }

  .user-info h3 {
    font-size: 1.2rem;
  }

  .balance-amount {
    font-size: 2.2rem;
  }

  .bonus-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .slider-dots {
    bottom: 12px;
    gap: 8px;
    padding: 5px 10px;
  }

  .slider-dot {
    width: 10px;
    height: 10px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 12px 0;
  }

  .hero-container {
    padding: 0 14px;
  }

  .hero-content {
    gap: 18px;
  }

  .hero-slider {
    height: 240px;
    border-radius: 18px;
  }

  .hero-cta-column {
    border-radius: 18px;
  }

  .user-profile-header {
    padding: 14px;
  }

  .balance-card, .bonus-card {
    margin: 0 14px 14px 14px;
    padding: 14px;
  }

  .main-cta-button {
    margin: 0 14px 14px 14px;
    padding: 14px;
    font-size: 0.95rem;
  }

  .user-avatar {
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }

  .user-info h3 {
    font-size: 1.1rem;
  }

  .user-info p {
    font-size: 0.75rem;
  }

  .balance-amount {
    font-size: 2rem;
  }

  .balance-details {
    font-size: 0.8rem;
  }

  .bonus-text {
    font-size: 0.85rem;
  }

  .secondary-cta-button {
    padding: 10px 16px;
    font-size: 0.8rem;
  }

  .slider-dots {
    bottom: 12px;
    gap: 8px;
    padding: 6px 12px;
  }

  .slider-dot {
    width: 10px;
    height: 10px;
  }
}

/* User Content Styles - Updated for Card Layout */
.user-content {
  position: relative;
  z-index: 4;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.user-status {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.user-rank {
  display: flex;
  justify-content: center;
}

.rank-badge {
  background: linear-gradient(135deg, #CD7F32 0%, #B8860B 100%);
  color: #1a1d29;
  padding: 6px 14px;
  border-radius: 20px;
  font-weight: 700;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(205, 127, 50, 0.3);
  border: none;
}

.rank-icon {
  font-size: 1.2rem;
}

.user-vip-status {
  display: flex;
  justify-content: center;
}

.vip-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 18px;
  background:
    linear-gradient(135deg, #FFD700 0%, #FFA500 100%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  border: 2px solid #FFD700;
  border-radius: 30px;
  color: #1A1A1A;
  font-weight: 800;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.6px;
  box-shadow:
    0 6px 20px rgba(255, 215, 0, 0.35),
    0 2px 8px rgba(0, 0, 0, 0.15),
    0 0 25px rgba(255, 215, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: modernVipGlow 3s ease-in-out infinite;
}

.vip-icon {
  font-size: 1.2rem;
}

.user-description {
  font-size: 1.05rem;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.5;
  margin: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  font-weight: 400;
  max-width: 280px;
  margin: 0 auto;
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.hero-cta-button.primary {
  background:
    linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.9) 100%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.hero-cta-button.secondary {
  background:
    linear-gradient(135deg, #FFD700 0%, #FFA500 100%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  color: #1A1A1A;
  box-shadow:
    0 6px 20px rgba(255, 215, 0, 0.35),
    0 2px 8px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border: 2px solid rgba(255, 215, 0, 0.3);
}

.hero-cta-button.secondary:hover {
  background:
    linear-gradient(135deg, #FFA500 0%, #FFD700 100%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.25) 0%, transparent 50%);
  box-shadow:
    0 10px 30px rgba(255, 215, 0, 0.45),
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 215, 0, 0.5);
}

/* Modern VIP Glow Animation */
@keyframes modernVipGlow {
  0%, 100% {
    box-shadow:
      0 6px 20px rgba(255, 215, 0, 0.35),
      0 2px 8px rgba(0, 0, 0, 0.15),
      0 0 25px rgba(255, 215, 0, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow:
      0 8px 25px rgba(255, 215, 0, 0.4),
      0 3px 10px rgba(0, 0, 0, 0.2),
      0 0 35px rgba(255, 215, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transform: scale(1.02);
  }
}

/* Responsive Design for User Content */
@media (max-width: 1024px) {
  .user-title {
    font-size: 1.8rem;
  }

  .user-description {
    font-size: 0.95rem;
    max-width: 240px;
  }

  .rank-badge, .vip-badge {
    font-size: 0.85rem;
    padding: 8px 14px;
  }

  .rank-icon, .vip-icon {
    font-size: 1rem;
  }

  .user-actions {
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .user-title {
    font-size: 1.6rem;
  }

  .user-status {
    gap: 12px;
  }

  .user-description {
    font-size: 0.9rem;
    max-width: 320px;
  }

  .rank-badge, .vip-badge {
    font-size: 0.8rem;
    padding: 8px 12px;
  }

  .user-actions {
    gap: 14px;
  }

  .user-content {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .user-title {
    font-size: 1.4rem;
  }

  .user-description {
    font-size: 0.85rem;
    max-width: 280px;
  }

  .user-actions {
    gap: 12px;
  }

  .rank-badge, .vip-badge {
    font-size: 0.75rem;
    padding: 7px 10px;
  }

  .user-content {
    gap: 18px;
  }
}

/* Last Played Slot Styles */
.last-played-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.last-played-game {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(var(--primary-color), 0.2);
  width: 100%;
  max-width: 280px;
  cursor: pointer;
  transition: transform 0.2s ease, border-color 0.2s ease;
}

.last-played-game:hover {
  transform: scale(1.02);
  border-color: rgba(var(--primary-color), 0.4);
}

.game-image-container {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(var(--primary-color), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.last-played-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.last-played-game:hover .play-overlay {
  opacity: 1;
}

.play-button {
  background: rgba(var(--primary-color), 0.9);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.play-button:hover {
  background: rgb(var(--primary-color));
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.play-icon {
  color: white;
  font-size: 12px;
  margin-left: 2px; /* Slight offset to center the triangle visually */
}

.play-again-text {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  text-align: center;
  font-weight: 400;
}

.play-again-text {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  text-align: center;
  font-weight: 400;
  margin-top: 8px;
}

.game-info {
  flex: 1;
  min-width: 0;
}

.game-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgb(var(--primary-color));
  margin: 0 0 4px 0;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.game-provider {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}



/* Responsive Design for Last Played Slot */
@media (max-width: 1024px) {
  .last-played-game {
    max-width: 260px;
    padding: 10px;
    gap: 10px;
  }

  .game-image-container {
    width: 50px;
    height: 50px;
  }

  .game-name {
    font-size: 0.85rem;
  }

  .game-provider {
    font-size: 0.7rem;
  }

  .play-again-text {
    font-size: 0.75rem;
  }

  .play-again-text {
    font-size: 0.7rem;
  }
}

@media (max-width: 768px) {
  .last-played-section {
    gap: 14px;
    padding: 16px 0;
  }

  .last-played-game {
    max-width: 240px;
    padding: 8px;
  }

  .game-image-container {
    width: 45px;
    height: 45px;
  }

  .game-name {
    font-size: 0.8rem;
  }

  .game-provider {
    font-size: 0.65rem;
  }

  .play-again-text {
    font-size: 0.7rem;
  }

  .play-again-text {
    font-size: 0.65rem;
  }
}

@media (max-width: 480px) {
  .last-played-section {
    gap: 12px;
    padding: 14px 0;
  }

  .last-played-game {
    max-width: 220px;
    padding: 8px;
  }

  .game-image-container {
    width: 40px;
    height: 40px;
  }

  .game-name {
    font-size: 0.75rem;
  }

  .game-provider {
    font-size: 0.6rem;
  }

  .play-again-text {
    font-size: 0.65rem;
  }
}
