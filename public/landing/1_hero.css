/* Hero Section Styles */
.hero-section {
  padding: 20px 0 0 0;
  position: relative;
  z-index: 1;
}

.hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.hero-content {
  display: grid;
  grid-template-columns: 9fr 4fr;
  gap: 20px;
  align-items: stretch;
}

/* Slider Column (3/4) */
.hero-slider-column {
  position: relative;
}

.hero-slider {
  background: rgba(var(--secondary-color), 0.3);
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  height: 400px;
  border: 1px solid rgba(var(--primary-color), 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.slider-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.slider-track {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: grab;
  will-change: transform;
}

.slider-track.dragging {
  transition: none;
  cursor: grabbing;
}

.slide {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
  cursor: pointer;
  position: relative;
}

.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 20px;
}

/* Mobile optimization for hero slider images */
@media (max-width: 768px) {
  .slide-image {
    object-position: left center;
  }
}

/* Slider Dots */
.slider-dots {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: row;
  gap: 12px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.slider-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.slider-dot.active {
  background: rgb(var(--primary-color));
  border-color: rgb(var(--primary-color));
  transform: scale(1.3);
  box-shadow:
    0 0 16px rgba(var(--primary-color), 0.6),
    0 2px 8px rgba(0, 0, 0, 0.4);
}

.slider-dot:hover {
  background: rgba(var(--primary-color), 0.8);
  border-color: rgba(var(--primary-color), 0.9);
  transform: scale(1.1);
  box-shadow:
    0 0 12px rgba(var(--primary-color), 0.4),
    0 2px 8px rgba(0, 0, 0, 0.3);
}

/* CTA Column (1/4) */
.hero-cta-column {
  position: relative;
  background:
    linear-gradient(135deg, rgba(var(--secondary-color), 0.95) 0%, rgba(var(--secondary-color), 0.85) 100%),
    linear-gradient(45deg, rgba(var(--primary-color), 0.03) 0%, transparent 50%);
  border-radius: 24px;
  padding: 32px 24px;
  border: 2px solid rgba(var(--primary-color), 0.15);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.25),
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* CTA Background Effects */
.hero-cta-column::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    /* Modern geometric pattern */
    radial-gradient(circle at 20% 20%, rgba(var(--primary-color), 0.08) 0%, transparent 40%),
    radial-gradient(circle at 80% 80%, rgba(var(--primary-color), 0.06) 0%, transparent 40%),
    radial-gradient(circle at 40% 60%, rgba(var(--primary-color), 0.04) 0%, transparent 30%),
    /* Subtle grid overlay */
    linear-gradient(90deg, rgba(var(--primary-color), 0.02) 1px, transparent 1px),
    linear-gradient(rgba(var(--primary-color), 0.02) 1px, transparent 1px);
  background-size: 100% 100%, 100% 100%, 100% 100%, 20px 20px, 20px 20px;
  pointer-events: none;
  z-index: 1;
  animation: modernGlow 6s ease-in-out infinite;
}

/* Modern accent border */
.hero-cta-column::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24px;
  padding: 2px;
  background: linear-gradient(135deg,
    rgba(var(--primary-color), 0.3) 0%,
    transparent 25%,
    transparent 75%,
    rgba(var(--primary-color), 0.2) 100%);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  pointer-events: none;
  z-index: 3;
}

.cta-content {
  position: relative;
  z-index: 4;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 24px;
  padding: 8px;
}

.hero-title {
  font-size: 2.4rem;
  font-weight: 800;
  color: rgb(var(--primary-color));
  margin: 0;
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(var(--primary-color), 0.3);
  line-height: 1.1;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg,
    rgb(var(--primary-color)) 0%,
    rgba(var(--primary-color), 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.hero-description {
  font-size: 1.05rem;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.5;
  margin: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  font-weight: 400;
  max-width: 280px;
  margin: 0 auto;
}

.hero-cta-button {
  background:
    linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.9) 100%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  border: 2px solid rgba(var(--primary-color), 0.3);
  border-radius: 18px;
  padding: 18px 36px;
  font-size: 1.15rem;
  font-weight: 700;
  color: rgb(var(--secondary-color));
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  box-shadow:
    0 6px 20px rgba(var(--primary-color), 0.35),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.hero-cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.25),
    transparent);
  transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.hero-cta-button:hover::before {
  left: 100%;
}

.hero-cta-button:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 10px 30px rgba(var(--primary-color), 0.45),
    0 4px 12px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  background:
    linear-gradient(135deg, rgba(var(--primary-color), 1.1) 0%, rgb(var(--primary-color)) 100%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.15) 0%, transparent 50%);
  border-color: rgba(var(--primary-color), 0.5);
}

.hero-cta-button:active {
  transform: translateY(-1px) scale(1.01);
  transition: all 0.15s ease;
}

.cta-text {
  position: relative;
  z-index: 2;
  font-weight: 700;
}

.cta-icon {
  font-size: 1.3rem;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.hero-cta-button:hover .cta-icon {
  transform: scale(1.1) rotate(5deg);
}

/* Animations */
@keyframes modernGlow {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.02);
  }
}

/* Pulse effect for CTA button */
@keyframes ctaPulse {
  0%, 100% {
    box-shadow:
      0 6px 20px rgba(var(--primary-color), 0.35),
      0 2px 8px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow:
      0 8px 25px rgba(var(--primary-color), 0.4),
      0 3px 10px rgba(0, 0, 0, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.25);
  }
}

.hero-cta-button {
  animation: ctaPulse 3s ease-in-out infinite;
}

/* Loading State */
.hero-slider.loading {
  background: rgba(var(--secondary-color), 0.2);
}

.hero-slider.loading::after {
  content: 'Slaytlar yükleniyor...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  z-index: 5;
}

/* Error State */
.hero-slider.error::after {
  content: 'Slaytlar yüklenemedi';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 100, 100, 0.8);
  font-size: 1.1rem;
  z-index: 5;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .hero-container {
    padding: 0 20px;
  }

  .hero-content {
    gap: 24px;
  }

  .hero-slider {
    height: 350px;
  }

  .hero-cta-column {
    padding: 28px 20px;
    border-radius: 20px;
  }

  .hero-title {
    font-size: 2.1rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-cta-button {
    padding: 16px 32px;
    font-size: 1.1rem;
  }

  .slider-dots {
    bottom: 16px;
    padding: 6px 14px;
  }
}

@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 2fr 1fr;
    gap: 20px;
  }

  .hero-slider {
    height: 320px;
  }

  .hero-cta-column {
    padding: 24px 18px;
    border-radius: 18px;
  }

  .hero-title {
    font-size: 1.9rem;
  }

  .hero-description {
    font-size: 0.95rem;
    max-width: 240px;
  }

  .hero-cta-button {
    padding: 15px 28px;
    font-size: 1.05rem;
    border-radius: 16px;
  }

  .cta-content {
    gap: 20px;
  }

  .slider-dots {
    bottom: 14px;
    gap: 10px;
    padding: 6px 12px;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 15px 0;
  }

  .hero-container {
    padding: 0 16px;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .hero-slider {
    height: 280px;
  }

  .hero-cta-column {
    padding: 24px 20px;
    border-radius: 20px;
  }

  .hero-title {
    font-size: 1.7rem;
  }

  .hero-description {
    font-size: 0.9rem;
    line-height: 1.4;
    max-width: 320px;
  }

  .hero-cta-button {
    padding: 16px 32px;
    font-size: 1.05rem;
    border-radius: 16px;
  }

  .cta-content {
    gap: 22px;
  }

  .slider-dots {
    bottom: 12px;
    gap: 8px;
    padding: 5px 10px;
  }

  .slider-dot {
    width: 10px;
    height: 10px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 12px 0;
  }

  .hero-container {
    padding: 0 14px;
  }

  .hero-content {
    gap: 18px;
  }

  .hero-slider {
    height: 240px;
    border-radius: 18px;
  }

  .hero-cta-column {
    padding: 22px 18px;
    border-radius: 18px;
  }

  .hero-title {
    font-size: 1.5rem;
  }

  .hero-description {
    font-size: 0.85rem;
    max-width: 280px;
  }

  .hero-cta-button {
    padding: 14px 28px;
    font-size: 0.95rem;
    border-radius: 14px;
  }

  .cta-content {
    gap: 20px;
  }

  .slider-dots {
    bottom: 12px;
    gap: 8px;
    padding: 6px 12px;
  }

  .slider-dot {
    width: 10px;
    height: 10px;
  }
}

/* User Content Styles */
.user-content {
  position: relative;
  z-index: 4;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 18px;
  padding: 8px;
}

.user-welcome {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.user-title {
  font-size: 2.1rem;
  font-weight: 800;
  color: rgb(var(--primary-color));
  margin: 0;
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(var(--primary-color), 0.3);
  line-height: 1.1;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg,
    rgb(var(--primary-color)) 0%,
    rgba(var(--primary-color), 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.user-status {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.user-rank {
  display: flex;
  justify-content: center;
}

.rank-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 18px;
  background:
    linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 100%),
    linear-gradient(45deg, rgba(205, 127, 50, 0.1) 0%, transparent 50%);
  border: 2px solid #CD7F32;
  border-radius: 30px;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  color: #CD7F32;
  font-weight: 700;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.6px;
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.25),
    0 2px 8px rgba(205, 127, 50, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.rank-icon {
  font-size: 1.2rem;
}

.user-vip-status {
  display: flex;
  justify-content: center;
}

.vip-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 18px;
  background:
    linear-gradient(135deg, #FFD700 0%, #FFA500 100%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  border: 2px solid #FFD700;
  border-radius: 30px;
  color: #1A1A1A;
  font-weight: 800;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.6px;
  box-shadow:
    0 6px 20px rgba(255, 215, 0, 0.35),
    0 2px 8px rgba(0, 0, 0, 0.15),
    0 0 25px rgba(255, 215, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: modernVipGlow 3s ease-in-out infinite;
}

.vip-icon {
  font-size: 1.2rem;
}

.user-description {
  font-size: 1.05rem;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.5;
  margin: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  font-weight: 400;
  max-width: 280px;
  margin: 0 auto;
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.hero-cta-button.primary {
  background:
    linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.9) 100%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.hero-cta-button.secondary {
  background:
    linear-gradient(135deg, #FFD700 0%, #FFA500 100%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  color: #1A1A1A;
  box-shadow:
    0 6px 20px rgba(255, 215, 0, 0.35),
    0 2px 8px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border: 2px solid rgba(255, 215, 0, 0.3);
}

.hero-cta-button.secondary:hover {
  background:
    linear-gradient(135deg, #FFA500 0%, #FFD700 100%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.25) 0%, transparent 50%);
  box-shadow:
    0 10px 30px rgba(255, 215, 0, 0.45),
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 215, 0, 0.5);
}

/* Modern VIP Glow Animation */
@keyframes modernVipGlow {
  0%, 100% {
    box-shadow:
      0 6px 20px rgba(255, 215, 0, 0.35),
      0 2px 8px rgba(0, 0, 0, 0.15),
      0 0 25px rgba(255, 215, 0, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow:
      0 8px 25px rgba(255, 215, 0, 0.4),
      0 3px 10px rgba(0, 0, 0, 0.2),
      0 0 35px rgba(255, 215, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transform: scale(1.02);
  }
}

/* Responsive Design for User Content */
@media (max-width: 1024px) {
  .user-title {
    font-size: 1.8rem;
  }

  .user-description {
    font-size: 0.95rem;
    max-width: 240px;
  }

  .rank-badge, .vip-badge {
    font-size: 0.85rem;
    padding: 8px 14px;
  }

  .rank-icon, .vip-icon {
    font-size: 1rem;
  }

  .user-actions {
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .user-title {
    font-size: 1.6rem;
  }

  .user-status {
    gap: 12px;
  }

  .user-description {
    font-size: 0.9rem;
    max-width: 320px;
  }

  .rank-badge, .vip-badge {
    font-size: 0.8rem;
    padding: 8px 12px;
  }

  .user-actions {
    gap: 14px;
  }

  .user-content {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .user-title {
    font-size: 1.2rem;
  }

  .user-actions {
    gap: 10px;
  }

  .rank-badge, .vip-badge {
    font-size: 0.7rem;
    padding: 5px 8px;
  }

  .user-description {
    font-size: 0.8rem;
  }
}

/* Last Played Slot Styles */
.last-played-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.last-played-game {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(var(--primary-color), 0.2);
  width: 100%;
  max-width: 280px;
  cursor: pointer;
  transition: transform 0.2s ease, border-color 0.2s ease;
}

.last-played-game:hover {
  transform: scale(1.02);
  border-color: rgba(var(--primary-color), 0.4);
}

.game-image-container {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(var(--primary-color), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.last-played-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.last-played-game:hover .play-overlay {
  opacity: 1;
}

.play-button {
  background: rgba(var(--primary-color), 0.9);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.play-button:hover {
  background: rgb(var(--primary-color));
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.play-icon {
  color: white;
  font-size: 12px;
  margin-left: 2px; /* Slight offset to center the triangle visually */
}

.play-again-text {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  text-align: center;
  font-weight: 400;
}

.play-again-text {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  text-align: center;
  font-weight: 400;
  margin-top: 8px;
}

.game-info {
  flex: 1;
  min-width: 0;
}

.game-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgb(var(--primary-color));
  margin: 0 0 4px 0;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.game-provider {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}



/* Responsive Design for Last Played Slot */
@media (max-width: 1024px) {
  .last-played-game {
    max-width: 260px;
    padding: 10px;
    gap: 10px;
  }

  .game-image-container {
    width: 50px;
    height: 50px;
  }

  .game-name {
    font-size: 0.85rem;
  }

  .game-provider {
    font-size: 0.7rem;
  }

  .play-again-text {
    font-size: 0.75rem;
  }

  .play-again-text {
    font-size: 0.7rem;
  }
}

@media (max-width: 768px) {
  .last-played-section {
    gap: 14px;
    padding: 16px 0;
  }

  .last-played-game {
    max-width: 240px;
    padding: 8px;
  }

  .game-image-container {
    width: 45px;
    height: 45px;
  }

  .game-name {
    font-size: 0.8rem;
  }

  .game-provider {
    font-size: 0.65rem;
  }

  .play-again-text {
    font-size: 0.7rem;
  }

  .play-again-text {
    font-size: 0.65rem;
  }
}

@media (max-width: 480px) {
  .last-played-section {
    gap: 12px;
    padding: 14px 0;
  }

  .last-played-game {
    max-width: 220px;
    padding: 8px;
  }

  .game-image-container {
    width: 40px;
    height: 40px;
  }

  .game-name {
    font-size: 0.75rem;
  }

  .game-provider {
    font-size: 0.6rem;
  }

  .play-again-text {
    font-size: 0.65rem;
  }
}
